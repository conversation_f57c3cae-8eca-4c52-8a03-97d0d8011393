import React, { useEffect } from 'react';
import { useChatStore, useThemeStore } from '../../store';
import { useSendMessage } from '../../hooks/api';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import { Minimize2, Maximize2, X, Settings } from 'lucide-react';
import clsx from 'clsx';
import { VIEW_MODE, type ChatContainerProps, type ViewMode } from '../../types';

const ChatContainer: React.FC<ChatContainerProps> = ({
  viewMode: propViewMode = "fullpage",
  onViewModeChange,
  onMinimize,
  onClose,
  className,
  children,
}) => {
  const {
    messages,
    isTyping,
    viewMode: storeViewMode,
    isMinimized,
    unreadCount,
    currentThreadId,
    setViewMode,
    setMinimized,
    markAsRead,
    clearConversation,
  } = useChatStore();
  
  const { toggleTheme } = useThemeStore();
  const sendMessageMutation = useSendMessage();
  
  const currentViewMode = propViewMode || storeViewMode;
  
  // Mark messages as read when chat is opened
  useEffect(() => {
    if (!isMinimized && unreadCount > 0) {
      markAsRead();
    }
  }, [isMinimized, unreadCount, markAsRead]);
  
  const handleSendMessage = async (content: string) => {
    try {
      // Send message via API with correct payload structure
      await sendMessageMutation.mutateAsync({
        message: content,
        accountId: '0',
        customerId: '1',
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to send message:', error);
      // The mutation will handle updating the message status to error
    }
  };
  

  
  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode);
    onViewModeChange?.(mode);
  };
  
  const handleMinimize = () => {
    setMinimized(true);
    onMinimize?.();
  };

  const handleRestore = () => {
    setMinimized(false);
  };

  const handleMaximize = () => {
    if (currentViewMode === VIEW_MODE.FLOATING) {
      handleViewModeChange(VIEW_MODE.FULLSCREEN);
    } else {
      handleViewModeChange(VIEW_MODE.FLOATING);
    }
    // Ensure chat is not minimized when changing view modes
    if (isMinimized) {
      setMinimized(false);
    }
  };
  
  const handleClose = () => {
    onClose?.();
  };
  
  const isFloating = currentViewMode === VIEW_MODE.FLOATING;
  const isFullscreen = currentViewMode === VIEW_MODE.FULLSCREEN;
  const isFullpage = currentViewMode === VIEW_MODE.FULLPAGE;
  
  return (
    <div
      className={clsx(
        'chat-container',
        {
          'chat-container--floating': isFloating,
          'chat-container--fullscreen': isFullscreen,
          'chat-container--fullpage': isFullpage,
          'chat-container--minimized': isMinimized,
        },
        className
      )}
    >
      {/* Header */}
      <div
        className="chat-container__header"
        onClick={isMinimized ? handleRestore : undefined}
        style={isMinimized ? { cursor: 'pointer' } : undefined}
      >
        <div className="chat-container__header-content">
          <div className="chat-container__title">
            <h2>Eneco Assistant</h2>
            {/* <ConnectionStatus /> */}
          </div>
          
          <div className="chat-container__header-actions">
            <button
              onClick={toggleTheme}
              className="chat-container__action"
              aria-label="Toggle theme"
            >
              <Settings size={18} />
            </button>

            {/* Maximize button - shown when floating and not minimized */}
            {isFloating && !isMinimized && (
              <button
                onClick={handleMaximize}
                className="chat-container__action"
                aria-label="Maximize chat"
              >
                <Maximize2 size={18} />
              </button>
            )}

            {/* Minimize to floating button - shown when fullscreen or fullpage */}
            {(isFullscreen || isFullpage) && (
              <button
                onClick={handleMaximize}
                className="chat-container__action"
                aria-label="Minimize to floating"
              >
                <Minimize2 size={18} />
              </button>
            )}

            {/* Minimize/Restore button - shown when floating */}
            {isFloating && (
              <button
                onClick={isMinimized ? handleRestore : handleMinimize}
                className="chat-container__action"
                aria-label={isMinimized ? "Restore chat" : "Minimize chat"}
              >
                <Minimize2 size={18} />
              </button>
            )}

            {onClose && (
              <button
                onClick={handleClose}
                className="chat-container__action chat-container__action--close"
                aria-label="Close chat"
              >
                <X size={18} />
              </button>
            )}
          </div>
        </div>
        
        {unreadCount > 0 && isMinimized && (
          <div className="chat-container__unread-badge">
            {unreadCount}
          </div>
        )}
      </div>
      
      {/* Main Content */}
      {!isMinimized && (
        <div className="chat-container__content">
          <div className="chat-container__messages">
            <MessageList
              messages={messages}
              isTyping={isTyping}
            />
          </div>
          
          <div className="chat-container__input">
            {/* Debug info - remove in production */}
            {currentThreadId && (
              <div style={{
                fontSize: '10px',
                color: '#666',
                padding: '4px 8px',
                background: '#f0f0f0',
                borderRadius: '4px',
                marginBottom: '8px'
              }}>
                Thread ID: {currentThreadId}
              </div>
            )}

            <MessageInput
              onSendMessage={handleSendMessage}
              disabled={sendMessageMutation.isPending}
              placeholder="Ask me anything about Eneco..."
            />
          </div>
        </div>
      )}
      
      {children}
    </div>
  );
};



export default ChatContainer;
