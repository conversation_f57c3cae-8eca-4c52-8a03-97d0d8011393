import { useMutation } from '@tanstack/react-query';
import { type SendMessagePayload, MUTATION_KEYS } from '../../types/api';
import chatService from '../../services/chatService';
import { useChatStore } from '../../store';

// Simplified hook for sending messages
export const useSendMessage = () => {
  const { addMessage, setTyping } = useChatStore();

  return useMutation({
    mutationKey: [MUTATION_KEYS.SEND_MESSAGE],
    mutationFn: async (payload: SendMessagePayload) => {
      // Add user message immediately
      addMessage(payload.message, 'user');

      // Show typing indicator
      setTyping(true);

      // Call API
      const response = await chatService.sendMessage(payload);
      return response;
    },
    onSuccess: (data) => {
      // Hide typing indicator
      setTyping(false);

      // Add the AI response
      console.log('API Response:', data);
      console.log('Response type:', typeof data);
      console.log('Response keys:', Object.keys(data || {}));

      // Try to extract the response text from different possible structures
      let responseText = '';
      if (typeof data === 'string') {
        responseText = data;
      } else if (data && typeof data === 'object') {
        // Try different possible field names using any type to handle unknown API structure
        const apiData = data as any;

        // Check if this is our expected structure
        if (apiData.response) {
          responseText = apiData.response;
        }
        // Check if the API returns the text directly in other fields
        else if (apiData.message) {
          responseText = apiData.message;
        }
        else if (apiData.text) {
          responseText = apiData.text;
        }
        else if (apiData.content) {
          responseText = apiData.content;
        }
        // If it's a simple string response
        else if (typeof apiData === 'string') {
          responseText = apiData;
        }
        // Fallback to JSON string
        else {
          responseText = JSON.stringify(data);
        }
      } else {
        responseText = 'Received response from AI';
      }

      addMessage(responseText, 'agent');
    },
    onError: (error) => {
      // Hide typing indicator
      setTyping(false);

      // Add error message
      console.error('API Error:', error);
      addMessage('Sorry, I encountered an error. Please try again.', 'agent');
    },
  });
};

// Simplified utility hook for chat operations
export const useChatOperations = () => {
  const sendMessage = useSendMessage();

  return {
    sendMessage: sendMessage.mutate,
    sendMessageAsync: sendMessage.mutateAsync,
    isSendingMessage: sendMessage.isPending,
  };
};
